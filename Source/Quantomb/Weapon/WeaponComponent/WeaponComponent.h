// Copyright © 2025 Super State Studio. All Rights Reserved. This file is part of Quantomb, developed by Super State Studio. Redistribution or modification of this code is not permitted without prior written consent from Super State Studio.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "WeaponComponent.generated.h"

/**
 * Enum defining different weapon component types
 */

UENUM(BlueprintType)
enum class EWeaponComponentType : uint8
{
	Base,
	Barrel,
	PistolGrip,
	FrontGrip,
	IronSights,
	Scope,
	Magazine,
	Stock,
};

UCLASS()
class QUANTOMB_API AWeaponComponent : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AWeaponComponent();

	// Weapon component type
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Component")
	EWeaponComponentType WeaponComponentType;

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;
	
};
