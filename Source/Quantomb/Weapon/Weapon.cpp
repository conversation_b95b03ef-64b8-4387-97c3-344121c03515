// Copyright © 2025 Super State Studio. All Rights Reserved. This file is part of Quantomb, developed by Super State Studio. Redistribution or modification of this code is not permitted without prior written consent from Super State Studio.


#include "Weapon.h"


// Sets default values
AWeapon::AWeapon()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void AWeapon::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void AWeapon::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

// Gets transform of aim point from component of either type IronSights or Scope
AWeaponComponent* AWeapon::GetWeaponComponentByType(EWeaponComponentType WeaponComponentType)
{
	// Create weapon component array from all attached weapon components
	TInlineComponentArray<AWeaponComponent*> WeaponComponents(this);
	
	for (AWeaponComponent* WeaponComponent : WeaponComponents)
	{
		if (WeaponComponent->WeaponComponentType == WeaponComponentType)
		{
			return WeaponComponent;
		}
	}
	return nullptr;
}

/**
 * Gets transform of aim point from a component of either type IronSights or Scope
 * @param WeaponComponentType The type of weapon component to get the aim point from
 * @return The transform of the aim point
 */

FTransform AWeapon::GetADSAimPointTransform(EWeaponComponentType WeaponComponentType)
{
	// Get the component
	AWeaponComponent* WeaponComponent = GetWeaponComponentByType(WeaponComponentType);
	if (WeaponComponent)
	{
		// Get the transform of the socket "aimpoint" from the components skeletal mesh

		// Get the skeletal mesh
		USkeletalMeshComponent* SkeletalMeshComponent = WeaponComponent->GetSkeletalMeshComponent();
		
		return WeaponComponent->GetS()->GetSocketTransform("aimpoint", RTS_World);
	}
	return FTransform::Identity;
}


