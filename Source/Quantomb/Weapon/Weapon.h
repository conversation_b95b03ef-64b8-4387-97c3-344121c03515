// Copyright © 2025 Super State Studio. All Rights Reserved. This file is part of Quantomb, developed by Super State Studio. Redistribution or modification of this code is not permitted without prior written consent from Super State Studio.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "WeaponComponent/WeaponComponent.h"
#include "Weapon.generated.h"

enum class EWeaponComponentType : uint8;

UCLASS()
class QUANTOMB_API AWeapon : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AWeapon();

	// Gets weapon component by type
	UFUNCTION(BlueprintCallable, Category = "Weapon")
	AWeaponComponent* GetWeaponComponentByType(EWeaponComponentType WeaponComponentType);

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;


};
